from pydantic import BaseModel, AnyHttpUrl
from typing import List
import os
import json
# --- Nested Models ---
class APISettings(BaseModel):
    tts: AnyHttpUrl
    asr: AnyHttpUrl
    intent: AnyHttpUrl
    place: AnyHttpUrl
    srm: AnyHttpUrl
    slam: AnyHttpUrl

class MDNSSettings(BaseModel):
    enable: bool
    name: str
    port: int
    hostname: str
    type: str
    version: str

class PathsSettings(BaseModel):
    smart_responses: str

class WebSocketSettings(BaseModel):
    allowed_clients: List[str]


# --- Root Config ---
class Settings(BaseModel):
    api: APISettings
    mdns: MDNSSettings
    paths: PathsSettings
    websocket: WebSocketSettings
    debug: bool


# --- Load from JSON ---
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
CONFIG_PATH = os.path.join(BASE_DIR, "configs", "config.json")

with open(CONFIG_PATH, "r", encoding="utf-8") as f:
    config_data = json.load(f)

settings = Settings(**config_data)
