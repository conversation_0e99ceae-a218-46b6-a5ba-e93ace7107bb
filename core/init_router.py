from fastapi import FastAPI
from controllers.asr_controller import ASRController
from controllers.manual_controller import Manual<PERSON>ontroller
from controllers.tts_controller import TTS<PERSON>ontroller
from controllers.ws_controller import WebSocketController
from controllers.intent_controller import IntentController
from controllers.vision_controller import VisionController
from controllers.iot_controller import Iot<PERSON><PERSON>roller


def init_controller(app: FastAPI):
    app.include_router(ASRController().router)
    app.include_router(TTSController().router)
    app.include_router(IntentController().router)
    app.include_router(WebSocketController().router)
    app.include_router(ManualController().router)
    app.include_router(VisionController().router)
    app.include_router(IotController().router)

