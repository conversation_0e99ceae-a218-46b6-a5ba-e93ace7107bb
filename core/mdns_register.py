from zeroconf import Zeroconf, ServiceInfo
import socket
import threading
from core.config import settings
from utils.logger import get_logger

logger = get_logger("mdns_register")

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def listen_mdns_queries():
    MCAST_GRP = '***********'
    MCAST_PORT = 5353

    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM, socket.IPPROTO_UDP)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

        # ✅ Bind vào địa chỉ 0.0.0.0 để nhận multicast từ mọi nguồn
        sock.bind(('', MCAST_PORT))  # kh<PERSON>c bi<PERSON>t quan trọng ở đây

        # Đăng ký nhận multicast từ *********** trên tất cả giao diện mạng
        mreq = socket.inet_aton(MCAST_GRP) + socket.inet_aton("0.0.0.0")
        sock.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)

        logger.info("Đang lắng nghe truy vấn mDNS từ client...")

        while True:
            data, addr = sock.recvfrom(1024)
            logger.info(f"📥 Nhận truy vấn mDNS từ {addr[0]}:{addr[1]} - {len(data)} bytes")
    except Exception as e:
        logger.error(f"Lỗi khi lắng nghe mDNS: {e}")

def _register():
    try:
        zeroconf = Zeroconf()
        ip = get_local_ip()

        service_info = ServiceInfo(
            type_=settings.mdns.type,
            name=f"{settings.mdns.name}.{settings.mdns.type}",
            port=settings.mdns.port,
            addresses=[socket.inet_aton(ip)],
            properties={
                b"version": settings.mdns.version.encode()
            },
            server=f"{settings.mdns.hostname}."
        )

        zeroconf.register_service(service_info, allow_name_change=True)
        logger.info(f"mDNS: Đăng ký dịch vụ tên '{service_info.name}' tại {ip}:{settings.mdns.port} với hostname '{settings.mdns.hostname}'")

    except Exception as e:
        logger.error(f"Lỗi khi _register: {e}")

def register_mdns_service():
    if settings.mdns.enable:
        threading.Thread(target=_register, daemon=True).start()
        #threading.Thread(target=listen_mdns_queries, daemon=True).start()
    else:
        logger.warning("mDNS bị tắt qua config (ENABLE_MDNS=False)")
