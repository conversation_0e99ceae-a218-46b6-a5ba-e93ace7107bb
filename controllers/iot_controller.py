from fastapi import APIRouter, Query
from services.aws_iot.publisher import publish_message

class IotController:
    def __init__(self):
        self.router = APIRouter()
        self.router.add_api_route(
            "/iot/publish",
            self.publish_to_iot,
            methods=["POST"],
            summary="Publish message to AWS IoT"
        )

    async def publish_to_iot(self, topic: str = Query(...), message: str = Query(...)):
        try:
            publish_message(topic, message)
            return {"status": "ok", "topic": topic, "message": message}
        except Exception as e:
            return {"status": "error", "message": str(e)}
