from fastapi import APIRouter
from schemas.tts_schema import TTSRequest
from services.tts_service import process_tts

class TTSController:
    def __init__(self):
        self.router = APIRouter(prefix="/tts", tags=["TTS"])
        self.router.post("")(self.speak) 
    async def speak(self, req: TTSRequest):
        result = await process_tts(req.text)
        return {
            "status": "ok",
            "message": "Speech generated",
            "data": result
        }
