from fastapi import APIRouter
from schemas.manual_schema import ManualRequestMartech, ManualRequestSound 
from services.websocket.handlers.martech_handlers import handle_martech_order
from services.websocket.handlers.sound_handlers import handle_sound_order

class ManualController:
    def __init__(self):
        self.router = APIRouter(prefix="/manual", tags=["manual"])
        self.router.post("/sound")(self.speak_sound)
        self.router.post("/martech")(self.show_martech)

    async def speak_sound(self, req: ManualRequestSound):
        # result = await process_tts(req.text)
        # uri_sound = result.get("file")
        
        # await send_task_manual_sound(uri_sound,req.name_app)
        
        await handle_sound_order(req.type,req.payload)

        return {
            "status": "ok",
            "message": "Speech generated",
            "data": {
                "text": req.payload.text,
                "result": "ok"
            }
        }

    async def show_martech(self, req: ManualRequestMartech):
        # await send_task_manual_martech(req.text,req.name_app)
        await handle_martech_order(req.type,req.payload)

        return {
            "status": "ok",
            "message": "Martech generated",
            "data": {
                "text": "",
                "result": "Ok"
            }
        }

