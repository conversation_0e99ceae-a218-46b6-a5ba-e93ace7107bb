from fastapi import APIRouter, WebSocket, status
from fastapi.websockets import WebSocketDisconnect
from services.websocket.websocket_service import connect_client, disconnect_client, receive_text
from core.config import settings

class WebSocketController:
    def __init__(self):
        self.router = APIRouter(tags=["WebSocket"])
        self.router.websocket("/ws/{client_id}")(self.websocket_endpoint)

    async def websocket_endpoint(self, websocket: WebSocket, client_id: str):
        # ✅ Kiểm tra client_id có trong danh sách cho phép không
        if client_id not in settings.websocket.allowed_clients:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            print(f"❌ Từ chối WebSocket: client_id '{client_id}' không được phép")
            return

        await connect_client(client_id, websocket)

        try:
            while True:
                text = await websocket.receive_text()
                await receive_text(client_id, text)
        except WebSocketDisconnect:
            print(f"🔌 Mất kết nối WebSocket: {client_id}")
            await disconnect_client(client_id)
        except Exception as e:
            print(f"⚠️ Lỗi WebSocket với {client_id}: {e}")
            await disconnect_client(client_id)
