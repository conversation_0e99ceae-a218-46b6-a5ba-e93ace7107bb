from fastapi import APIRouter
from pydantic import BaseModel
from services.websocket.websocket_service import send_to


class VisionRequest(BaseModel):
    text: str
    isStart: bool


class VisionController:
    def __init__(self):
        self.router = APIRouter(prefix="/vision", tags=["vision"])
        self.router.post("/follow")(self.flow_session)

    async def flow_session(self, req: VisionRequest):
        if req.isStart:
            payload = {
                "type": req.text,
                "payload": {
                    "text": "Start",
                    "app": "SmartController",
                    "is_start": True
                }
            }
        else:
            payload = {
                "type": req.text,
                "payload": {
                    "text": "Start",
                    "app": "SmartController",
                    "is_start": False
                }
            }
        
        await send_to("vision_app", payload)
        return {
                "status": "ok",
                "message": "🚀 Đã gửi tín hiệu",
                "data": payload["payload"]
            }

