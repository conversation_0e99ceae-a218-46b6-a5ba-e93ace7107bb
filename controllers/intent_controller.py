from fastapi import APIRouter
from pydantic import BaseModel
from typing import List, Optional
from services.intent_service import process_intent

class IntentRequest(BaseModel):
    text: str
    valid_location: Optional[List[str]] = []

class IntentController:
    def __init__(self):
        self.router = APIRouter(prefix="/intent", tags=["Intent"])
        self.router.post("/parse")(self.parse_intent)

    async def parse_intent(self, req: IntentRequest):
        result = await process_intent(req.text, req.valid_location)
        return {
            "status": "ok",
            "message": "Intent parsed successfully",
            "data": result
        }
