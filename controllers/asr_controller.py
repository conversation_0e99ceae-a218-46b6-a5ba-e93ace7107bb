from fastapi import APIRouter
from schemas.asr_schema import ASRRequest
from usecases.handle_asr_logic import handle_asr_logic

class ASRController:
    def __init__(self):
        self.router = APIRouter(prefix="/asr", tags=["ASR Controller"])
        self.router.post("/run")(self.run_asr)

    async def run_asr(self, req: ASRRequest):
        await handle_asr_logic(req.text)
        return {
            "status": "ok",
            "message": "<PERSON><PERSON><PERSON> đã nhận, đang xử lý...",
            "data": None
        }
