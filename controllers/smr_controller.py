from fastapi import APIRouter
from schemas.smr_schema import SMRRequest
from services.tts_service import process_tts

class SMRController:
    def __init__(self):
        self.router = APIRouter(prefix="/question", tags=["SMR"])
        self.router.post("")(self.speak) 
    async def speak(self, req: SMRRequest):
        result = await process_tts(req.text)
        return {
            "status": "ok",
            "message": "Speech generated",
            "data": result
        }
