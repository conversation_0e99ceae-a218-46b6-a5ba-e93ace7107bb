from services.aws_iot.mqtt_client import get_iot_client
from configs.aws_config import AWS_IOT_CONFIG
from utils.logger import get_logger
logger = get_logger("aws_iot_manager")

class AwsIotManager:
    _client = None

    @classmethod
    def init(cls):
        if cls._client is None:
            try:
                cls._client = get_iot_client()
                logger.info("[AWS IoT] Connected successfully.")
                cls.subscribe_to_command_topic()
            except Exception as e:
                logger.exception("[AWS IoT] ❌ Lỗi khi khởi tạo kết nối AWS IoT")

    @classmethod
    def get_client(cls):
        if cls._client is None:
            raise Exception("AWS IoT client not initialized. Call AwsIotManager.init() first.")
        return cls._client

    @classmethod
    def subscribe_to_command_topic(cls):
        topic = AWS_IOT_CONFIG["topic_command"]

        def command_callback(client, userdata, message):
            payload = message.payload.decode()
            logger.info(f"[AWS IoT] 📥 Lệnh điều khiển nhận được: {payload}")
            # handle_robot_command(payload)  # Tùy bạn

        try:
            cls._client.subscribe(topic, 1, command_callback)
            logger.info(f"[AWS IoT] 📡 Đăng ký nhận lệnh tại topic: {topic}")
        except Exception as e:
            logger.exception(f"[AWS IoT] ❌ Lỗi khi subscribe topic {topic}")
