from services.aws_iot.aws_iot_manager import AwsIotManager
from configs.aws_config import AWS_IOT_CONFIG
from utils.logger import get_logger

logger = get_logger("aws_iot_publisher")

def publish_message(topic, message):
    try:
        client = AwsIotManager.get_client()
        client.publish(topic, message, 1)
        logger.info(f"[AWS IoT] 📤 Published to {topic}: {message}")
    except Exception as e:
        logger.exception(f"[AWS IoT] ❌ Publish thất bại: {e}")
