import os
from dotenv import load_dotenv
from pathlib import Path

# ✅ Tự xác định thư mục gốc project (nơi chứa .env)
BASE_DIR = Path(__file__).resolve().parents[0].parent
ENV_PATH = BASE_DIR / '.env'

# ✅ Load .env từ đúng vị trí tuyệt đối
load_dotenv(dotenv_path=ENV_PATH)

AWS_IOT_CONFIG = {
    "endpoint": os.getenv("AWS_IOT_ENDPOINT"),
    "client_id": os.getenv("AWS_IOT_CLIENT_ID"),
    "root_ca": os.getenv("AWS_IOT_ROOT_CA"),
    "private_key": os.getenv("AWS_IOT_PRIVATE_KEY"),
    "certificate": os.getenv("AWS_IOT_CERT"),
    "topic_status": os.getenv("AWS_IOT_TOPIC_STATUS"),
    "topic_command": os.getenv("AWS_IOT_TOPIC_COMMAND"),
}
