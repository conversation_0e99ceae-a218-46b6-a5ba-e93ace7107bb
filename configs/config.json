{"api": {"tts": "http://0.0.0.0:8003", "asr": "http://0.0.0.0:8004", "intent": "http://0.0.0.0:8080", "place": "http://0.0.0.0:8005", "srm": "http://0.0.0.0:8002", "slam": "http://************:1448"}, "mdns": {"enable": true, "name": "SmartController", "port": 8000, "hostname": "controller.local", "type": "_robot._tcp.local.", "version": "1.0"}, "paths": {"smart_responses": "app/configs/smart_responses.json"}, "websocket": {"allowed_clients": ["device_app", "martech_app", "device_slam", "vision_app", "ai_intent", "ai_srm"]}, "debug": true}