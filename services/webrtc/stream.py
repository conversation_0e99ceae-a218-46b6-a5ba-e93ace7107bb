# app/services/webrtc/stream.py

import av
import pyrealsense2 as rs
from aiortc import VideoStreamTrack
import numpy as np

class RealSenseVideoTrack(VideoStreamTrack):
    kind = "video"

    def __init__(self, format="bgr8", width=640, height=480, fps=30):
        super().__init__()
        self.pipeline = rs.pipeline()
        config = rs.config()

        # Mapping format string to rs.format enum safely
        try:
            rs_format = getattr(rs.format, format.lower())
        except AttributeError:
            rs_format = rs.format.bgr8  # fallback nếu sai format

        # Enable depth và color stream
        config.enable_stream(rs.stream.depth, width, height, rs.format.z16, fps)
        config.enable_stream(rs.stream.color, width, height, rs_format, fps)

        self.config = config
        self.pipeline.start(self.config)

        # 🔧 Lấy metadata sau khi start pipeline
        try:
            profile = self.pipeline.get_active_profile()
            device = profile.get_device()

            depth_sensor = next((s for s in device.query_sensors() if s.is_depth_sensor()), None)
            depth_scale = depth_sensor.get_depth_scale() if depth_sensor else 0.001

            # Lấy intrinsics từ depth stream
            depth_stream = profile.get_stream(rs.stream.depth).as_video_stream_profile()
            intr = depth_stream.get_intrinsics()

            self.metadata = {
                "depth_scale": depth_scale,
                "intrinsics": {
                    "width": intr.width,
                    "height": intr.height,
                    "fx": intr.fx,
                    "fy": intr.fy,
                    "ppx": intr.ppx,
                    "ppy": intr.ppy,
                    "model": str(intr.model)
                },
                "distortion": list(intr.coeffs)
            }
        except Exception as e:
            self.metadata = {"error": str(e)}

    async def recv(self):
        pts, time_base = await self.next_timestamp()
        frames = self.pipeline.wait_for_frames()
        color_frame = frames.get_color_frame()

        if not color_frame:
            return None

        image = np.asanyarray(color_frame.get_data())
        frame = av.VideoFrame.from_ndarray(image, format="bgr24")
        frame.pts = pts
        frame.time_base = time_base
        return frame