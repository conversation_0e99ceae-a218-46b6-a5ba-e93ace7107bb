import httpx
from core.config import settings

async def send_move_to_action_location(x: float, y: float, yaw: float):
    payload = {
        "action_name": "slamtec.agent.actions.MoveToAction",
        "options": {
            "target": {
                "x": x,
                "y": y,
                "z": 0
            },
            "move_options": {
                "mode": 0,
                "flags": ["with_yaw", "precise"],
                "yaw": yaw,
                "acceptable_precision": 0,
                "fail_retry_count": 0
            }
        }
    }

    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(
                f"{settings.SLAM_API}api/core/motion/v1/actions",
                json=payload,
                headers={
                    "accept": "application/json",
                    "Content-Type": "application/json"
                },
                timeout=5
            )
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}

async def send_move_to_action_direction(typeDirection: int):
    payload = {
        "action_name": "slamtec.agent.actions.MoveByAction",
        "options": {
            "direction": 1
        }
    }

    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(
                f"{settings.SLAM_API}api/core/motion/v1/actions",
                json=payload,
                headers={
                    "accept": "application/json",
                    "Content-Type": "application/json"
                },
                timeout=5
            )
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}