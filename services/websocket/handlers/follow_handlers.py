from utils.logger import get_logger
from services.websocket.websocket_service import send_to

logger = get_logger("follow_handlers")

async def handle_follow_order(client_id: str, payload: dict):
    logger.info(f"[{client_id}] 🛒 Follow ORDER: {payload}")

    name = payload.get("name", client_id)
    is_start = payload.get("isStart", False)

    # <PERSON><PERSON><PERSON> l<PERSON>nh ch<PERSON>h tới vision_app
    control_msg = {
        "type": "follow_order",
        "payload": {
            "text": "Start" if is_start else "Stop",
            "app": name,
            "is_start": is_start
        }
    }
    await send_to("vision_app", control_msg)

    # G<PERSON>i ack lại cho client
    ack_msg = {
        "type": "ack",
        "payload": {
            "command": "follow_order",
            "status": "received",
            "is_start": is_start,
            "app": name
        }
    }
    await send_to(client_id, ack_msg)
