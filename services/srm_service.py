import base64
import httpx
from core.config import settings
from utils.factory import convert_string_to_base64
from utils.logger import get_logger
import json
import os
import asyncio
from services.tts_service import process_tts

logger = get_logger("srm_service")


async def process_smr(text: str):
    try:
        dataCreateTask = get_response_by_query(text)
        
        asyncio.create_task(generate_content_for_response(text))

        return dataCreateTask
    except Exception as e:
        return {"error": str(e)}

async def call_smr(text: str):
    payload = {
        "query": text
    }
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(f"{settings.api.srm}question", json=payload, timeout=5)
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}

def get_response_by_query(query: str):
    if not os.path.exists(settings.paths.smart_responses):
        raise FileNotFoundError(f"File {settings.paths.smart_responses} không tồn tại.")
        
    with open(settings.paths.smart_responses, "r", encoding="utf-8") as f:
        data = json.load(f)

    for item in data:
        if item.get("query") == query:
            return {
                "sound_uri": item.get("sound_uri", ""),
                "markdown_base64": item.get("markdown_base64", ""),
                "final_markdown": item.get("final_markdown", "")
            }
    

    logger.error(f"Không tìm thấy kết quả cho query: {query}")
    return None

async def generate_content_for_response(query:str):
    try:
        if not query:
            raise ValueError("Thiếu trường 'text' trong payload.")

        # Xử lý nội dung
        smr_result = await call_smr(query)
        summary = smr_result.get("summary")
        markdown = smr_result.get("markdown")
        sources = smr_result.get("sources")
        logger.info(f"sources: {sources}")
        contentImageMarkdown = convert_to_markdown_images(sources)
        final_markdown = markdown + "\n\n" + contentImageMarkdown

        sound_uri = await process_tts(summary)
        sound_uri_get = f"{settings.api.tts}download/{sound_uri.get('file')}"
        markdown_base64 = convert_string_to_base64(final_markdown)

        # Dữ liệu cập nhật đầy đủ
        updated_data = {
            "query": query,
            "summary": summary,
            "markdown": markdown,
            "sources": sources,
            "sound_uri": sound_uri_get,
            "markdown_base64": markdown_base64,
            "final_markdown": final_markdown
        }

        # Đọc file hiện tại
        if os.path.exists(settings.paths.smart_responses):
            with open(settings.paths.smart_responses, "r", encoding="utf-8") as f:
                responses = json.load(f)
        else:
            responses = []

        updated = False
        for i, item in enumerate(responses):
            if item.get("query") == query:
                responses[i].update(updated_data)
                updated = True
                logger.info(f"🔁 Đã cập nhật nội dung cho query: {query}")
                break

        if not updated:
            responses.append(updated_data)
            logger.info(f"🆕 Đã thêm mới nội dung cho query: {query}")

        # Ghi lại file JSON
        with open(settings.paths.smart_responses, "w", encoding="utf-8") as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ Đã ghi thành công vào: {settings.paths.smart_responses}")

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"❌ Lỗi xử lý content: {e}")

    try:
        if not query:
            raise ValueError("Thiếu trường 'text' trong payload.")

        smr_result = await call_smr(query)
        summary = smr_result.get("summary")
        markdown = smr_result.get("markdown")
        sources = smr_result.get("sources")

        contentImageMarkdown = convert_to_markdown_images(sources)
        final_markdown = markdown + "\n\n" + contentImageMarkdown

        sound_uri = await process_tts(summary)
        sound_uri_get = f"{settings.api.tts}download/{sound_uri.get('file')}"
        markdown_base64 = convert_string_to_base64(final_markdown)

        # Chuẩn bị dict mới đầy đủ trường để update
        updated_data = {
            "query": query,
            "summary": summary,
            "markdown": markdown,
            "sources": sources,
            "sound_uri": sound_uri_get,
            "markdown_base64": markdown_base64,
            "final_markdown": final_markdown
        }

        # Đọc file hiện tại
        with open(settings.paths.smart_responses, "r", encoding="utf-8") as f:
            responses = json.load(f)

        updated = False
        for i, item in enumerate(responses):
            if item.get("query") == query:
                responses[i].update(updated_data)
                updated = True
                break

        if not updated:
            responses.append(updated_data)

        # Ghi lại file JSON
        with open(settings.paths.smart_responses, "w", encoding="utf-8") as f:
            json.dump(responses, f, ensure_ascii=False, indent=2)

        logger.info(f"✅ Đã ghi thành công vào: {settings.paths.smart_responses}")
        logger.info(f"✅ Đã cập nhật thành công nội dung cho query: {query}")

    except Exception as e:
        logger.error(f"❌ Lỗi xử lý content: {e}")

def convert_to_markdown_images(source_dict: dict) -> str:
    result_lines = []
    for key, url in source_dict.items():
        result_lines.append(f"![{key.replace('_', ' ').title()}]({url})")
    return "\n".join(result_lines)