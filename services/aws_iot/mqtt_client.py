from configs.aws_config import AWS_IOT_CONFIG
from AWSIoTPythonSDK.MQTTLib import AWSIoTMQTTClient
from utils.logger import get_logger
import os

logger = get_logger("aws_iot_manager")

def validate_aws_config():
    required_keys = ["client_id", "endpoint", "root_ca", "private_key", "certificate"]
    for key in required_keys:
        value = AWS_IOT_CONFIG.get(key)
        if not value:
            raise ValueError(f"[AWS IoT] ❌ Thiếu cấu hình: {key}")

        if key in ["root_ca", "private_key", "certificate"] and not os.path.isfile(value):
            raise FileNotFoundError(f"[AWS IoT] ❌ File không tồn tại: {key} → {value}")

def get_iot_client():
    try:
        validate_aws_config()
    except Exception as e:
        logger.exception(f"[AWS IoT] ❌ C<PERSON><PERSON> hình không hợp lệ: {e}")
        raise

    client = AWSIoTMQTTClient(AWS_IOT_CONFIG["client_id"])
    
    # ✅ Thiết lập endpoint và chứng chỉ
    client.configureEndpoint(AWS_IOT_CONFIG["endpoint"], 8883)
    client.configureCredentials(
        AWS_IOT_CONFIG["root_ca"],
        AWS_IOT_CONFIG["private_key"],
        AWS_IOT_CONFIG["certificate"]
    )

    # ✅ Cấu hình kết nối / hoạt động
    client.configureOfflinePublishQueueing(-1)
    client.configureDrainingFrequency(2)
    client.configureConnectDisconnectTimeout(10)
    client.configureMQTTOperationTimeout(5)
    client.configureAutoReconnectBackoffTime(1, 32, 20)

    def on_disconnect(client, userdata, rc):
        logger.info("[AWS IoT] 🔌 Mất kết nối.")

    def on_reconnect(client, userdata, flags, rc):
        logger.info("[AWS IoT] 🔁 Đã reconnect thành công.")

    client.onOffline = on_disconnect
    client.onOnline = on_reconnect

    try:
        client.connect()
        logger.info("[AWS IoT] ✅ Đã kết nối thành công.")
    except Exception as e:
        logger.exception(f"[AWS IoT] ❌ Lỗi khi kết nối: {e}")
        raise

    return client
