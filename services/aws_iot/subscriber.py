from .mqtt_client import get_iot_client
from utils.logger import get_logger

logger = get_logger("aws_iot_subscriber")

def custom_callback(client, userdata, message):
    payload = message.payload.decode()
    logger.info(f"[AWS IoT] 📥 Nhận từ {message.topic}: {payload}")

def subscribe(topic):
    try:
        client = get_iot_client()
        client.subscribe(topic, 1, custom_callback)
        logger.info(f"[AWS IoT] ✅ Subscribed to topic: {topic}")
    except Exception as e:
        logger.exception(f"[AWS IoT] ❌ Lỗi khi subscribe topic {topic}")
