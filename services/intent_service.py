import httpx
from core.config import settings

async def process_intent(text: str, valid_location: list = None):
    payload = {
        "text": text,
        "valid_location": valid_location or []
    }
    try:
        async with httpx.AsyncClient() as client:
            resp = await client.post(f"{settings.api.intent}parse", json=payload, timeout=5)
            resp.raise_for_status()
            return resp.json()
    except Exception as e:
        return {"error": str(e)}
