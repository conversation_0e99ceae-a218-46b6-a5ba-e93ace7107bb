# app/services/action_registry.py
from typing import Callable, Awaitable, Dict

# Dict[client_id, Dict[action_type, callback]]
ActionRegistry: Dict[str, Dict[str, Callable[[str, dict], Awaitable[None]]]] = {}

def register_action(client_id: str, action: str, callback: Callable[[str, dict], Awaitable[None]]):
    if client_id not in ActionRegistry:
        ActionRegistry[client_id] = {}
    ActionRegistry[client_id][action] = callback

def get_handler(client_id: str, action: str):
    # Ưu tiên handler dành riêng cho client, n<PERSON><PERSON> không có thì fallback về "*"
    return (
        ActionRegistry.get(client_id, {}).get(action)
        or ActionRegistry.get("*", {}).get(action)
    )
