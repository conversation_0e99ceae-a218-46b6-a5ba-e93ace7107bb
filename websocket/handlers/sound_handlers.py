from typing import Union, Mapping, Any
from schemas.manual_schema import ManualPayload
from utils.logger import get_logger
from services.tts_service import process_tts
from usecases.handle_task_logic import send_task_sound

logger = get_logger("sound_handlers")

def to_manual_payload(obj: Union[ManualPayload, Mapping[str, Any]]) -> ManualPayload:
    """Trả về ManualPayload từ dict hoặc ManualPayload; hỗ trợ Pydantic v1/v2."""
    if isinstance(obj, ManualPayload):
        return obj
    # pydantic v2 có model_validate; v1 dùng parse_obj
    if hasattr(ManualPayload, "model_validate"):
        return ManualPayload.model_validate(obj)   # v2
    return ManualPayload.parse_obj(obj)           # v1

async def handle_sound_order(type_order: str, payload: Union[ManualPayload, Mapping[str, Any]]):
    try:
        data = to_manual_payload(payload)
        # Log gọn gàng, không crash nếu là model v1/v2
        to_log = data.model_dump() if hasattr(data, "model_dump") else data.dict()
        logger.info(f"Nhận đơn hàng {type_order}: {to_log}")

        result = await process_tts(data.text)
        uri_sound = (result or {}).get("file")

        await send_task_sound(uri_sound, data.name_app)

    except Exception as e:
        logger.exception(f"❌ Lỗi khi xử lý sound_order: {e}")
