from utils.logger import get_logger

logger = get_logger("device_handlers")

# async def handle_martech_order(client_id: str, payload: dict):
#     logger.info(f"Nhận đơn hàng từ {client_id}: {payload}")

#     await send_task_from_device(payload)
#     # <PERSON><PERSON><PERSON> phản hồi lại client nếu cần
#     # await send_to(client_id, {
#     #     "type": "martech_order_ack",
#     #     "payload": {"status": "received", "client": client_id}
#     # })