# app/services/websocket/handlers/camera_handlers.py

from aiortc import RTCPeerConnection, RTCSessionDescription
from services.webrtc.stream import RealSenseVideoTrack
from hardware.camera_info import get_camera_info
from services.websocket.websocket_service import send_to
from utils.logger import get_logger
import pyrealsense2 as rs
import asyncio

# Global dict lưu task gửi metadata theo client
metadata_tasks = {}
logger = get_logger("camera_handlers")
peers = {}


def parse_resolution(res_str):
    try:
        w, h = map(int, res_str.lower().split("x"))
        return w, h
    except:
        return 640, 480


async def handle_webrtc_order(client_id: str, payload: dict):
    name = payload.get("name", "unknown")
    ip = payload.get("ip", "unknown")

    logger.info(f"[{client_id}] 📥 Nhận yêu cầu cấu hình camera từ {name}@{ip}")

    try:
        info = get_camera_info()
        await send_to(client_id, {
            "type": "camera_info",
            "payload": info["streams"]
        })
    except Exception as e:
        logger.error(f"[{client_id}] ❌ Lỗi khi xử lý webrtc_order: {e}")
        await send_to(client_id, {
            "type": "error",
            "payload": { "message": str(e) }
        })


async def handle_webrtc_offer(client_id: str, payload: dict):
    try:
        logger.info(f"[{client_id}] 📥 Nhận yêu cầu webrtc_order từ {payload.get('name')}")

        selected_format = payload.get("selected_format", "bgr8")
        resolution = payload.get("resolution", "640x480")
        fps = int(payload.get("fps", 30))
        width, height = parse_resolution(resolution)

        track = RealSenseVideoTrack(format=selected_format, width=width, height=height, fps=fps)

        pc = RTCPeerConnection()
        pc.addTrack(track)
        peers[client_id] = pc

        offer_sdp = payload.get("sdp")

        metadata = track.metadata if hasattr(track, "metadata") else {}
        await send_to(client_id, {
            "type": "webrtc_metadata",
            "payload": metadata
        })

        old_task = metadata_tasks.get(client_id)
        if old_task:
            old_task.cancel()

        task = asyncio.create_task(stream_metadata_loop(client_id, track))
        metadata_tasks[client_id] = task

        offer = await pc.createOffer()
        await pc.setLocalDescription(offer)

        await send_to(client_id, {
            "type": "webrtc_offer",
            "payload": {
                "sdp": pc.localDescription.sdp,
                "camera_info": get_camera_info()
            }
        })

    except Exception as e:
        logger.error(f"[{client_id}] ❌ Lỗi khi tạo offer: {e}")
        await send_to(client_id, {
            "type": "error",
            "payload": { "message": str(e) }
        })


async def handle_webrtc_answer(client_id: str, payload: dict):
    logger.info(f"[{client_id}] 🎬 Nhận SDP ANSWER")

    pc = peers.get(client_id)
    if not pc:
        await send_to(client_id, {
            "type": "error",
            "payload": { "message": "❌ Không tìm thấy peer connection" }
        })
        return

    try:
        answer_sdp = payload.get("sdp")
        if answer_sdp:
            await pc.setRemoteDescription(RTCSessionDescription(
                sdp=answer_sdp,
                type="answer"
            ))
            logger.info(f"[{client_id}] ✅ SDP answer đã được thiết lập")
    except Exception as e:
        logger.error(f"[{client_id}] ❌ Lỗi khi set SDP answer: {e}")
        await send_to(client_id, {
            "type": "error",
            "payload": { "message": str(e) }
        })


async def stream_metadata_loop(client_id: str, track: RealSenseVideoTrack, interval: float = 0.2):
    while True:
        try:
            metadata = track.metadata if hasattr(track, "metadata") else {}
            await send_to(client_id, {
                "type": "webrtc_metadata",
                "payload": metadata
            })
            await asyncio.sleep(interval)
        except Exception as e:
            logger.warning(f"[{client_id}] ⚠️ Dừng gửi metadata do lỗi: {e}")
            break


def close_peer(client_id: str):
    pc = peers.pop(client_id, None)
    if pc:
        asyncio.create_task(pc.close())
        logger.info(f"[{client_id}] 🔌 Peer connection closed")

    task = metadata_tasks.pop(client_id, None)
    if task:
        task.cancel()
        logger.info(f"[{client_id}] 🛑 Đã dừng gửi metadata")