from typing import Union

from schemas.manual_schema import ManualPayloadMartech
from services.srm_service import process_smr
from usecases.handle_task_logic import send_task_martech
from utils.factory import convert_string_to_base64
from utils.logger import get_logger

logger = get_logger("martech_handlers")

async def handle_martech_order(type_order: str, payload: Union[dict, ManualPayloadMartech]):
    try:
        # Chuẩn hóa payload về ManualPayloadMartech
        data = payload if isinstance(payload, ManualPayloadMartech) else ManualPayloadMartech(**payload)
        logger.info(f"Nhận đơn hàng {type_order}: {data}")

        # Mặc định: lấy text gốc -> base64
        dataMartech = convert_string_to_base64(data.text or "")

        # Nếu cần gọi SMR để tạo markdown cuối cùng
        if getattr(data, "isCallSmr", False):
            smr_result = process_smr(data.text or "")
            if isinstance(smr_result, dict):
                final_markdown = smr_result.get("final_markdown") or ""
                dataMartech = convert_string_to_base64(final_markdown)
            else:
                logger.warning("process_smr không trả về dict, dùng text gốc thay thế.")

        await send_task_martech(dataMartech, data.name_app)

    except Exception as e:
        logger.exception(f"❌ Lỗi khi xử lý martech_order: {e}")
