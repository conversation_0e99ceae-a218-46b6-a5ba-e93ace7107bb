# app/services/socket_commands.py

import json
from core.config import settings
from services.websocket.websocket_service import send_to, client_ping_status
from utils.logger import get_logger

logger = get_logger("socket_commands")


async def handle_ping(client_id: str, payload: dict):
    client_ping_status[client_id] = {
        "ip": payload.get("ip", ""),
        "name": client_id,
        "status": True
    }

    logger.info(f"Cập nhật ping từ {client_id}: {client_ping_status[client_id]}")

    full_status = []
    for cid in settings.websocket.allowed_clients:
        cid = cid.strip()
        info = client_ping_status.get(cid, {
            "ip": "",
            "name": cid,
            "status": False
        })
        info["name"] = cid
        full_status.append(info)

    await send_to(client_id, {
        "type": "pong",
        "payload": json.loads(json.dumps(full_status, separators=(',', ':')))
    })


async def handle_echo(client_id: str, payload: dict):
    await send_to(client_id, {
        "type": "echo",
        "payload": payload
    })
