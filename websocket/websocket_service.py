from fastapi import WebSocket
from typing import Dict
from utils.logger import get_logger
from core.config import settings
import json
from services.websocket.action_registry import get_handler

logger = get_logger("websocket")

active_connections: Dict[str, WebSocket] = {}

# ✅ Khởi tạo trạng thái ping từ WS_CLIENT_IDS
client_ping_status: Dict[str, dict] = {}
for cid in settings.websocket.allowed_clients:
    cid = cid.strip()
    if cid:
        client_ping_status[cid] = {
            "ip": "",
            "name": "",
            "status": False
        }

logger.info(f"Khởi tạo trạng thái WebSocket clients: {client_ping_status}")

async def connect_client(client_id: str, websocket: WebSocket):
    await websocket.accept()
    active_connections[client_id] = websocket
    logger.info(f"WebSocket kết nối: {client_id}")

async def disconnect_client(client_id: str):
    if client_id in active_connections:
        del active_connections[client_id]
        logger.info(f"WebSocket ngắt kết nối: {client_id}")

    # ✅ Cập nhật trạng thái client thành offline
    if client_id in client_ping_status:
        client_ping_status[client_id]["status"] = False
        logger.info(f"Cập nhật trạng thái {client_id} => OFFLINE: {client_ping_status[client_id]}")


async def receive_text(client_id: str, text: str):
    logger.debug(f"Nhận từ {client_id}: {text}")
    try:
        message = json.loads(text)

        msg_type = message.get("type")
        payload = message.get("payload")

        handler = get_handler(client_id, msg_type)

        if handler:
            await handler(client_id, payload)
        else:
            logger.warning(f"⚠️ Không có handler cho action '{msg_type}' từ client '{client_id}'")
            await send_to(client_id, {"error": f"No handler registered for action '{msg_type}'"})

        # msg_type = message.get("type")
        # payload = message.get("payload")
        # if msg_type == "ping" and isinstance(payload, dict):
        #     await handle_ping(client_id, payload)
        # elif msg_type == "martech_order" and isinstance(payload, dict):
        #     await handle_martech_order(client_id, payload)
        # else:
        #     await send_to(client_id, {"echo": text})

    except Exception as e:
        logger.error(f"Lỗi xử lý message từ {client_id}: {e}", exc_info=True)
        await send_to(client_id, {"error": "Message format invalid"})

async def send_to(client_id: str, data: dict):
    ws = active_connections.get(client_id)
    if ws:
        try:
            await ws.send_json(data)
            logger.info(f"Gửi đến {client_id}: {data}")
        except Exception as e:
            logger.error(f"Lỗi khi gửi đến {client_id}: {e}", exc_info=True)
    else:
        logger.warning(f"Không tìm thấy client_id={client_id} trong active_connections")
