from services.websocket.action_registry import register_action
from services.websocket.handlers.camera_handlers import handle_webrtc_answer, handle_webrtc_offer, handle_webrtc_order
from services.websocket.handlers.martech_handlers import handle_martech_order
from services.websocket.handlers.follow_handlers import handle_follow_order
from services.websocket.handlers.sound_handlers import handle_sound_order
from services.websocket.socket_commands import handle_ping, handle_echo

def register_all_actions():
    # <PERSON><PERSON>ng cho tất cả client
    register_action("*", "ping", handle_ping)
    register_action("*", "echo", handle_echo)
    register_action("*", "martech_order", handle_martech_order)
    register_action("*", "sound_order", handle_sound_order)

    register_action("*", "webrtc_offer_request", handle_webrtc_offer)
    register_action("*", "webrtc_answer", handle_webrtc_answer)
    register_action("*", "webrtc_order", handle_webrtc_order)

    register_action("device_app", "follow_order", handle_follow_order)


